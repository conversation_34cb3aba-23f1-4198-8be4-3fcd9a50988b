using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using ThreadService.Data;
using ThreadService.Services;
using DotNetEnv;

// Load .env file BEFORE creating the WebApplicationBuilder
// This ensures environment variables are available during configuration
var envPaths = new[]
{
    "../.env",           // Root level (development)
    ".env",              // Current directory (development)
    "../../.env",        // Parent directory (development)
    "/app/.env",         // Container root (production)
    "/src/.env"          // Alternative container path
};

foreach (var envPath in envPaths)
{
    if (File.Exists(envPath))
    {
        DotNetEnv.Env.Load(envPath);
        Console.WriteLine($"DEBUG: Loaded .env from: {envPath}");
        break;
    }
}

var builder = WebApplication.CreateBuilder(args);

Console.WriteLine("DEBUG: Using ASP.NET Core configuration with .env file support");

// Configure logging
builder.Logging.ClearProviders();
builder.Logging.AddConsole();
if (builder.Environment.IsDevelopment())
{
    builder.Logging.AddDebug();
}

// Add services to the container.
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() { Title = "Thread Service API", Version = "v1" });
    c.AddSecurityDefinition("Bearer", new()
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = Microsoft.OpenApi.Models.ParameterLocation.Header,
        Type = Microsoft.OpenApi.Models.SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });
    c.AddSecurityRequirement(new()
    {
        {
            new()
            {
                Reference = new() { Type = Microsoft.OpenApi.Models.ReferenceType.SecurityScheme, Id = "Bearer" }
            },
            Array.Empty<string>()
        }
    });
});

// Configure Entity Framework with Supabase PostgreSQL
// Only register PostgreSQL if not in testing environment (tests will register their own InMemory database)
if (!builder.Environment.IsEnvironment("Testing"))
{
    // Environment variables automatically override appsettings.json values
    var connectionString = builder.Configuration["SUPABASE_CONNECTION_STRING"]
        ?? builder.Configuration.GetConnectionString("DefaultConnection")
        ?? throw new InvalidOperationException("No database connection string found. Set SUPABASE_CONNECTION_STRING environment variable or DefaultConnection in appsettings.json");

    // Log configuration status for debugging
    var logger = LoggerFactory.Create(config => config.AddConsole()).CreateLogger<Program>();
    logger.LogInformation("ThreadService starting up...");
    if (logger.IsEnabled(LogLevel.Information))
    {
        logger.LogInformation("Environment: {Environment}", builder.Environment.EnvironmentName);
    }
    logger.LogInformation("✅ Connection string loaded from ASP.NET Core configuration");

    builder.Services.AddDbContext<ThreadContext>(options =>
    {
        options.UseNpgsql(connectionString, npgsqlOptions =>
        {
            npgsqlOptions.EnableRetryOnFailure(
                maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(10),
                errorCodesToAdd: null);
            npgsqlOptions.CommandTimeout(30);
        });

        if (builder.Environment.IsDevelopment())
        {
            options.EnableSensitiveDataLogging();
            options.EnableDetailedErrors();
        }
    });
}

// AutoMapper can be added later if needed for complex mappings

// Add memory cache
builder.Services.AddMemoryCache();
builder.Services.AddScoped<ICacheService, MemoryCacheService>();

// Register services
builder.Services.AddScoped<IThreadService, ThreadService.Services.ThreadService>();
builder.Services.AddScoped<ICommentService, CommentService>();
builder.Services.AddScoped<ILikeShareService, LikeShareService>();

// Configure JWT Authentication (Supabase)
// Environment variables automatically override appsettings.json values
var supabaseUrl = builder.Configuration["SUPABASE_URL"];
var supabaseJwtSecret = builder.Configuration["SUPABASE_JWT_SECRET"];

bool hasValidSupabaseConfig = !string.IsNullOrEmpty(supabaseUrl) && !string.IsNullOrEmpty(supabaseJwtSecret);

// Only require Supabase configuration if not in testing environment
if (!hasValidSupabaseConfig && !builder.Environment.IsEnvironment("Testing"))
{
    throw new InvalidOperationException("SUPABASE_URL and SUPABASE_JWT_SECRET must be configured");
}

// Configure JWT Authentication only if not in testing environment
if (!builder.Environment.IsEnvironment("Testing") && hasValidSupabaseConfig)
{
    // Clear default claim type mappings to use original claim names
    System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler.DefaultInboundClaimTypeMap.Clear();

    builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
        .AddJwtBearer(options =>
        {
            // Use JWT secret for Supabase token validation
            var key = Encoding.UTF8.GetBytes(supabaseJwtSecret ?? throw new InvalidOperationException("SUPABASE_JWT_SECRET is required"));

            options.RequireHttpsMetadata = !builder.Environment.IsDevelopment();
            options.SaveToken = true;

            options.TokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuer = true,
                ValidIssuer = $"{supabaseUrl}/auth/v1",
                ValidateAudience = false, // Supabase doesn't use audience validation by default
                ValidateLifetime = true,
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ClockSkew = TimeSpan.FromMinutes(5),
                RequireExpirationTime = true,
                RequireSignedTokens = true
            };

            // Add event handlers for debugging and monitoring
            options.Events = new JwtBearerEvents
            {
                OnAuthenticationFailed = context =>
                {
                    var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
                    if (logger.IsEnabled(LogLevel.Warning))
                    {
                        logger.LogWarning("JWT Authentication failed in Thread service: {Error}", context.Exception.Message);
                    }
                    return Task.CompletedTask;
                },
                OnTokenValidated = context =>
                {
                    var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
                    if (logger.IsEnabled(LogLevel.Information))
                    {
                        var userId = context.Principal?.FindFirst("sub")?.Value;
                        logger.LogInformation("JWT Token validated in Thread service for user: {UserId}", userId);
                    }
                    return Task.CompletedTask;
                }
            };
        });
}

// Add authorization
builder.Services.AddAuthorization();

// Configure CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// Add health checks
var healthChecks = builder.Services.AddHealthChecks()
    .AddCheck("self", () => Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy("Service is running"))
    .AddCheck("configuration", () =>
    {
        var issues = new List<string>();

        // Only check Supabase configuration if not in testing environment
        if (!hasValidSupabaseConfig && !builder.Environment.IsEnvironment("Testing"))
        {
            issues.Add("Supabase authentication configuration is incomplete");
        }

        return issues.Count == 0
            ? Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy("Configuration is valid")
            : Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Degraded($"Configuration issues: {string.Join(", ", issues)}");
    });

// Only add database health check if not in testing environment
if (!builder.Environment.IsEnvironment("Testing"))
{
    var connectionString = builder.Configuration["SUPABASE_CONNECTION_STRING"]
        ?? builder.Configuration.GetConnectionString("DefaultConnection");

    if (!string.IsNullOrEmpty(connectionString))
    {
        healthChecks.AddNpgSql(connectionString, name: "database", timeout: TimeSpan.FromSeconds(30));
    }
}

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Thread Service API v1");
        c.RoutePrefix = "swagger"; // Serve Swagger UI at /swagger
        c.DocumentTitle = "Thread Service API Documentation";
    });
}
else
{
    app.UseExceptionHandler("/error");
    app.UseHsts();
    app.UseHttpsRedirection();
}
app.UseCors("AllowAll");
app.UseRouting();
app.UseAuthentication();
app.UseAuthorization();
app.MapControllers();

// Map health check endpoints
app.MapHealthChecks("/health");
app.MapHealthChecks("/health/ready", new Microsoft.AspNetCore.Diagnostics.HealthChecks.HealthCheckOptions
{
    Predicate = check => check.Name == "self"
});
app.MapHealthChecks("/health/live", new Microsoft.AspNetCore.Diagnostics.HealthChecks.HealthCheckOptions
{
    Predicate = _ => false // Just return healthy without running any checks
});

// Add error handling endpoint
app.Map("/error", (ILogger<Program> logger) =>
{
    logger.LogError("An unhandled exception occurred in Thread service");
    return Results.Problem("An error occurred while processing your request.");
});

// Add a root endpoint for API information
app.MapGet("/", () => new
{
    service = "Thread Service",
    version = "1.0.0",
    status = hasValidSupabaseConfig ? "Ready" : "Configuration Issues",
    documentation = new
    {
        swagger = "/swagger",
        apiTest = "/api/test"
    },
    configuration = new
    {
        hasSupabaseConnection = true,
        hasSupabaseAuth = hasValidSupabaseConfig,
        environment = app.Environment.EnvironmentName
    },
    endpoints = new
    {
        threads = ApiEndpoints.Threads,
        comments = ApiEndpoints.Comments,
        likes = ApiEndpoints.Likes,
        shares = ApiEndpoints.Shares,
        health = ApiEndpoints.Health
    }
});

app.Run();

// Static class for API endpoint documentation
static class ApiEndpoints
{
    public static readonly string[] Threads = [
        "GET /api/threads - Get all threads",
        "POST /api/threads - Create a new thread",
        "GET /api/threads/{id} - Get a specific thread",
        "PUT /api/threads/{id} - Update a thread",
        "DELETE /api/threads/{id} - Delete a thread",
        "GET /api/threads/user/{userId} - Get threads by user"
    ];

    public static readonly string[] Comments = [
        "GET /api/comments - Get all comments",
        "POST /api/comments - Add a comment to a thread",
        "GET /api/comments/{id} - Get a specific comment",
        "PUT /api/comments/{id} - Update a comment",
        "DELETE /api/comments/{id} - Delete a comment",
        "GET /api/comments/thread/{threadId} - Get comments for a thread"
    ];

    public static readonly string[] Likes = [
        "POST /api/likes/toggle - Toggle like on a thread"
    ];

    public static readonly string[] Shares = [
        "POST /api/likes/share/toggle - Toggle share on a thread"
    ];

    public static readonly string[] Health = [
        "GET /health - Health check",
        "GET /health/ready - Readiness check",
        "GET /health/live - Liveness check"
    ];
}
