using Microsoft.EntityFrameworkCore;
using AssistantService.Models;

namespace AssistantService.Data;

/// <summary>
/// Entity Framework DbContext for the Assistant Service
/// Connects to Supabase PostgreSQL database
/// </summary>
public class AssistantDbContext : DbContext
{
    public AssistantDbContext(DbContextOptions<AssistantDbContext> options) : base(options)
    {
    }

    /// <summary>
    /// DbSet for AI chat messages
    /// </summary>
    public DbSet<AiChatMessage> AiChatMessages { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure AiChatMessage entity
        modelBuilder.Entity<AiChatMessage>(entity =>
        {
            // Table mapping
            entity.ToTable("ai_chats");

            // Primary key
            entity.HasKey(e => e.Id);
            
            // Column configurations
            entity.Property(e => e.Id)
                .HasColumnName("id")
                .HasColumnType("uuid")
                .HasDefaultValueSql("gen_random_uuid()");

            entity.Property(e => e.UserId)
                .HasColumnName("user_id")
                .HasColumnType("uuid")
                .IsRequired();

            entity.Property(e => e.Message)
                .HasColumnName("message")
                .HasColumnType("text")
                .IsRequired();

            entity.Property(e => e.Role)
                .HasColumnName("role")
                .HasColumnType("text")
                .IsRequired()
                .HasMaxLength(20);

            entity.Property(e => e.SessionId)
                .HasColumnName("session_id")
                .HasColumnType("text")
                .IsRequired(false)
                .HasMaxLength(255);

            entity.Property(e => e.CreatedAt)
                .HasColumnName("created_at")
                .HasColumnType("timestamp with time zone")
                .HasDefaultValueSql("now()");

            // Optional columns (may not exist if SQL script wasn't run)
            entity.Property(e => e.Model)
                .HasColumnName("model")
                .HasColumnType("varchar(50)")
                .IsRequired(false)
                .HasDefaultValue("mistral");

            entity.Property(e => e.UpdatedAt)
                .HasColumnName("updated_at")
                .HasColumnType("timestamp with time zone")
                .IsRequired(false)
                .HasDefaultValueSql("now()");

            entity.Property(e => e.MetadataJson)
                .HasColumnName("metadata")
                .HasColumnType("jsonb")
                .IsRequired(false);

            entity.Property(e => e.ConversationTitle)
                .HasColumnName("conversation_title")
                .HasColumnType("varchar(255)")
                .IsRequired(false);

            entity.Property(e => e.MessageSequence)
                .HasColumnName("message_sequence")
                .HasColumnType("integer")
                .IsRequired(false)
                .HasDefaultValue(1);

            // Indexes for performance
            entity.HasIndex(e => e.UserId)
                .HasDatabaseName("idx_ai_chats_user_id");

            entity.HasIndex(e => e.SessionId)
                .HasDatabaseName("idx_ai_chats_session_id");

            entity.HasIndex(e => e.CreatedAt)
                .HasDatabaseName("idx_ai_chats_created_at");

            entity.HasIndex(e => new { e.UserId, e.SessionId })
                .HasDatabaseName("idx_ai_chats_user_session");
        });
    }

    /// <summary>
    /// Override SaveChanges to automatically update timestamps
    /// </summary>
    public override int SaveChanges()
    {
        UpdateTimestamps();
        return base.SaveChanges();
    }

    /// <summary>
    /// Override SaveChangesAsync to automatically update timestamps
    /// </summary>
    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        UpdateTimestamps();
        return await base.SaveChangesAsync(cancellationToken);
    }

    /// <summary>
    /// Update timestamps for entities being modified
    /// </summary>
    private void UpdateTimestamps()
    {
        var entries = ChangeTracker.Entries<AiChatMessage>();

        foreach (var entry in entries)
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    entry.Entity.CreatedAt = DateTime.UtcNow;
                    if (entry.Entity.UpdatedAt == null)
                        entry.Entity.UpdatedAt = DateTime.UtcNow;
                    break;

                case EntityState.Modified:
                    entry.Entity.UpdatedAt = DateTime.UtcNow;
                    // Prevent CreatedAt from being modified
                    entry.Property(e => e.CreatedAt).IsModified = false;
                    break;
            }
        }
    }
}
