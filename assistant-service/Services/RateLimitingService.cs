using Microsoft.Extensions.Caching.Memory;
using System.Collections.Concurrent;

namespace AssistantService.Services;

public class RateLimitingService : IRateLimitingService
{
    private readonly IMemoryCache _cache;
    private readonly ILogger<RateLimitingService> _logger;
    private readonly ConcurrentDictionary<string, ClientBlockInfo> _blockedClients = new();

    // Rate limiting configuration
    private static readonly Dictionary<string, RateLimitConfig> EndpointLimits = new()
    {
        { "/api/chat", new RateLimitConfig { RequestsPerMinute = 10, RequestsPerHour = 100, RequestsPerDay = 500 } },
        { "/api/assistant/ask", new RateLimitConfig { RequestsPerMinute = 15, RequestsPerHour = 150, RequestsPerDay = 750 } }
    };

    private static readonly RateLimitConfig DefaultLimits = new()
    {
        RequestsPerMinute = 5,
        RequestsPerHour = 50,
        RequestsPerDay = 200
    };

    public RateLimitingService(IMemoryCache cache, ILogger<RateLimitingService> logger)
    {
        _cache = cache;
        _logger = logger;
    }

    public RateLimitResult CheckRateLimit(string clientId, string endpoint)
    {
        if (string.IsNullOrEmpty(clientId))
        {
            return new RateLimitResult
            {
                IsAllowed = false,
                Reason = "Client ID is required",
                LimitType = RateLimitType.Blocked
            };
        }

        // Check if client is blocked
        if (IsClientBlocked(clientId))
        {
            var blockInfo = _blockedClients[clientId];
            return new RateLimitResult
            {
                IsAllowed = false,
                Reason = $"Client blocked: {blockInfo.Reason}",
                LimitType = RateLimitType.Blocked,
                ResetTime = blockInfo.BlockedUntil - DateTime.UtcNow
            };
        }

        var config = EndpointLimits.GetValueOrDefault(endpoint, DefaultLimits);
        var now = DateTime.UtcNow;

        // Check per-minute limit
        var minuteKey = $"{clientId}:{endpoint}:minute:{now:yyyy-MM-dd-HH-mm}";
        var minuteCount = _cache.GetOrCreate(minuteKey, entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(1);
            return 0;
        });

        if (minuteCount >= config.RequestsPerMinute)
        {
            _logger.LogWarning("Rate limit exceeded for client {ClientId} on endpoint {Endpoint}: {Count} requests per minute", 
                clientId, endpoint, minuteCount);
            
            return new RateLimitResult
            {
                IsAllowed = false,
                RequestsRemaining = 0,
                ResetTime = TimeSpan.FromSeconds(60 - now.Second),
                Reason = "Too many requests per minute",
                LimitType = RateLimitType.PerMinute
            };
        }

        // Check per-hour limit
        var hourKey = $"{clientId}:{endpoint}:hour:{now:yyyy-MM-dd-HH}";
        var hourCount = _cache.GetOrCreate(hourKey, entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(1);
            return 0;
        });

        if (hourCount >= config.RequestsPerHour)
        {
            _logger.LogWarning("Hourly rate limit exceeded for client {ClientId} on endpoint {Endpoint}: {Count} requests per hour", 
                clientId, endpoint, hourCount);
            
            return new RateLimitResult
            {
                IsAllowed = false,
                RequestsRemaining = 0,
                ResetTime = TimeSpan.FromMinutes(60 - now.Minute),
                Reason = "Too many requests per hour",
                LimitType = RateLimitType.PerHour
            };
        }

        // Check per-day limit
        var dayKey = $"{clientId}:{endpoint}:day:{now:yyyy-MM-dd}";
        var dayCount = _cache.GetOrCreate(dayKey, entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromDays(1);
            return 0;
        });

        if (dayCount >= config.RequestsPerDay)
        {
            _logger.LogWarning("Daily rate limit exceeded for client {ClientId} on endpoint {Endpoint}: {Count} requests per day", 
                clientId, endpoint, dayCount);
            
            return new RateLimitResult
            {
                IsAllowed = false,
                RequestsRemaining = 0,
                ResetTime = TimeSpan.FromHours(24 - now.Hour),
                Reason = "Too many requests per day",
                LimitType = RateLimitType.PerDay
            };
        }

        return new RateLimitResult
        {
            IsAllowed = true,
            RequestsRemaining = config.RequestsPerMinute - minuteCount,
            ResetTime = TimeSpan.FromSeconds(60 - now.Second),
            LimitType = RateLimitType.None
        };
    }

    public void RecordRequest(string clientId, string endpoint)
    {
        if (string.IsNullOrEmpty(clientId)) return;

        var now = DateTime.UtcNow;

        // Increment counters
        var minuteKey = $"{clientId}:{endpoint}:minute:{now:yyyy-MM-dd-HH-mm}";
        var hourKey = $"{clientId}:{endpoint}:hour:{now:yyyy-MM-dd-HH}";
        var dayKey = $"{clientId}:{endpoint}:day:{now:yyyy-MM-dd}";

        _cache.Set(minuteKey, _cache.Get<int>(minuteKey) + 1, TimeSpan.FromMinutes(1));
        _cache.Set(hourKey, _cache.Get<int>(hourKey) + 1, TimeSpan.FromHours(1));
        _cache.Set(dayKey, _cache.Get<int>(dayKey) + 1, TimeSpan.FromDays(1));

        // Check for suspicious activity patterns
        CheckForSuspiciousActivity(clientId, endpoint);
    }

    public bool IsClientBlocked(string clientId)
    {
        if (!_blockedClients.TryGetValue(clientId, out var blockInfo))
            return false;

        if (DateTime.UtcNow > blockInfo.BlockedUntil)
        {
            _blockedClients.TryRemove(clientId, out _);
            _logger.LogInformation("Client {ClientId} block expired", clientId);
            return false;
        }

        return true;
    }

    public void BlockClient(string clientId, TimeSpan duration, string reason)
    {
        var blockInfo = new ClientBlockInfo
        {
            ClientId = clientId,
            BlockedAt = DateTime.UtcNow,
            BlockedUntil = DateTime.UtcNow.Add(duration),
            Reason = reason
        };

        _blockedClients[clientId] = blockInfo;
        _logger.LogWarning("Client {ClientId} blocked for {Duration}: {Reason}", 
            clientId, duration, reason);
    }

    private void CheckForSuspiciousActivity(string clientId, string endpoint)
    {
        var now = DateTime.UtcNow;
        var minuteKey = $"{clientId}:{endpoint}:minute:{now:yyyy-MM-dd-HH-mm}";
        var minuteCount = _cache.Get<int>(minuteKey);

        // Auto-block clients with extremely high request rates
        if (minuteCount > 50) // More than 50 requests per minute
        {
            BlockClient(clientId, TimeSpan.FromHours(1), "Excessive request rate detected");
        }
        else if (minuteCount > 30) // More than 30 requests per minute
        {
            BlockClient(clientId, TimeSpan.FromMinutes(15), "High request rate detected");
        }

        // Check for rapid-fire requests (potential bot behavior)
        var recentRequestsKey = $"{clientId}:recent_requests";
        var recentRequests = _cache.GetOrCreate(recentRequestsKey, entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5);
            return new List<DateTime>();
        }) ?? new List<DateTime>();

        recentRequests.Add(now);
        
        // Remove requests older than 10 seconds
        recentRequests.RemoveAll(r => (now - r).TotalSeconds > 10);
        
        // If more than 10 requests in 10 seconds, block temporarily
        if (recentRequests.Count > 10)
        {
            BlockClient(clientId, TimeSpan.FromMinutes(5), "Rapid-fire requests detected");
        }

        _cache.Set(recentRequestsKey, recentRequests, TimeSpan.FromMinutes(5));
    }

    private class RateLimitConfig
    {
        public int RequestsPerMinute { get; set; }
        public int RequestsPerHour { get; set; }
        public int RequestsPerDay { get; set; }
    }

    private class ClientBlockInfo
    {
        public string ClientId { get; set; } = string.Empty;
        public DateTime BlockedAt { get; set; }
        public DateTime BlockedUntil { get; set; }
        public string Reason { get; set; } = string.Empty;
    }
}
