apiVersion: apps/v1
kind: Deployment
metadata:
  name: assistant-service
  namespace: abraapi
  labels:
    app: assistant-service
    service: assistant
    version: v1
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: assistant-service
  template:
    metadata:
      labels:
        app: assistant-service
        service: assistant
        version: v1
    spec:
      containers:
      - name: assistant-service
        image: ghcr.io/abraapp/assistant-service:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: ASPNETCORE_ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: assistant-service-config
              key: ASPNETCORE_ENVIRONMENT
        - name: ASPNETCORE_URLS
          valueFrom:
            configMapKeyRef:
              name: assistant-service-config
              key: ASPNETCORE_URLS
        - name: OLLAMA_URL
          valueFrom:
            secretKeyRef:
              name: assistant-service-secret
              key: OLLAMA_URL
        - name: SUPABASE_CONNECTION_STRING
          valueFrom:
            secretKeyRef:
              name: assistant-service-secret
              key: S<PERSON><PERSON><PERSON>E_CONNECTION_STRING
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 180
          periodSeconds: 45
          timeoutSeconds: 30
          failureThreshold: 5
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        securityContext:
          runAsNonRoot: true
          runAsUser: 1001
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
