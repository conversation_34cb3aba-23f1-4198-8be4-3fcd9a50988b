using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using AssistantService.Models;
using AssistantService.Services;
using AssistantService.Configuration;
using System.Text.Json;
using System.Text;

namespace AssistantService.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ChatController(IOllamaClient ollama, IPromptInjectionDetector injectionDetector, IContentFilter contentFilter, IOptions<SecurityOptions> securityOptions, IChatStorageService chatStorage) : ControllerBase
{
    private readonly IOllamaClient _ollama = ollama;
    private readonly IPromptInjectionDetector _injectionDetector = injectionDetector;
    private readonly IContentFilter _contentFilter = contentFilter;
    private readonly SecurityOptions _securityOptions = securityOptions.Value;
    private readonly IChatStorageService _chatStorage = chatStorage;

    [HttpPost]
    public async Task<IActionResult> Chat([FromBody] ChatRequest request)
    {
        // Validate input
        if (request == null)
        {
            return BadRequest("Request cannot be empty");
        }

        if (string.IsNullOrWhiteSpace(request.Model))
        {
            return BadRequest("Model is required");
        }

        if (request.Messages == null || request.Messages.Count == 0)
        {
            return BadRequest("At least one message is required");
        }

        // Get the last user message
        var userMessage = request.Messages.LastOrDefault(m => m.Role.Equals("user", StringComparison.OrdinalIgnoreCase));
        if (userMessage == null)
        {
            return BadRequest("No user message found");
        }

        // Check content length before any processing
        if (string.IsNullOrWhiteSpace(userMessage.Content))
        {
            return BadRequest("Message content cannot be empty");
        }

        if (userMessage.Content.Length > _securityOptions.MaxInputLength)
        {
            return BadRequest($"Message content is too long (max {_securityOptions.MaxInputLength} characters)");
        }

        string sanitizedContent = userMessage.Content;

        // Check for prompt injection attacks (only if enabled)
        if (_securityOptions.EnablePromptInjectionDetection)
        {
            var injectionResult = _injectionDetector.AnalyzeInput(userMessage.Content);

            if (injectionResult.RiskLevel == RiskLevel.Critical)
            {
                return BadRequest("Request blocked due to security concerns");
            }

            if (injectionResult.RiskLevel == RiskLevel.High)
            {
                return BadRequest("Request contains potentially harmful content");
            }

            sanitizedContent = injectionResult.SanitizedInput ?? userMessage.Content;
        }

        // Apply content filtering (only if enabled)
        if (_securityOptions.EnableContentFiltering)
        {
            var contentResult = _contentFilter.FilterContent(sanitizedContent);

            if (!contentResult.IsContentSafe)
            {
                return BadRequest($"Content policy violation: {contentResult.Reason}");
            }

            sanitizedContent = contentResult.FilteredContent ?? string.Empty;
        }

        // Final check after processing - content might be filtered to empty
        if (string.IsNullOrWhiteSpace(sanitizedContent))
        {
            return BadRequest("Message content cannot be empty");
        }

        try
        {
            // Generate session ID for this conversation
            var sessionId = Guid.NewGuid().ToString();

            // For now, use a default user ID since we don't have authentication
            // In production, this should come from the authenticated user
            var userId = Guid.Parse("00000000-0000-0000-0000-000000000001");

            // Save user messages to database
            try
            {
                foreach (var message in request.Messages.Where(m => m.Role.Equals("user", StringComparison.OrdinalIgnoreCase)))
                {
                    await _chatStorage.SaveMessageAsync(userId, new CreateChatMessageRequest
                    {
                        Message = message.Content,
                        Role = "user",
                        SessionId = sessionId,
                        Model = request.Model
                    });
                }
            }
            catch (Exception ex)
            {
                // Log the error but don't fail the request
                Console.WriteLine($"Failed to save user messages: {ex.Message}");
            }

            if (request.Stream)
            {
                return await StreamResponse(sanitizedContent, userId, sessionId, request.Model);
            }
            else
            {
                return await NonStreamResponse(sanitizedContent, userId, sessionId, request.Model);
            }
        }
        catch (InvalidOperationException ex)
        {
            return StatusCode(503, ex.Message);
        }
        catch (Exception)
        {
            return StatusCode(500, "Something went wrong. Please try again.");
        }
    }

    private async Task<IActionResult> StreamResponse(string content, Guid userId, string sessionId, string model)
    {
        Response.Headers.ContentType = "text/plain; charset=utf-8";
        Response.Headers.CacheControl = "no-cache";
        Response.Headers.Connection = "keep-alive";

        var reply = await _ollama.AskAsync(content);

        // Save AI response to database
        try
        {
            await _chatStorage.SaveMessageAsync(userId, new CreateChatMessageRequest
            {
                Message = reply,
                Role = "assistant",
                SessionId = sessionId,
                Model = model
            });
        }
        catch (Exception ex)
        {
            // Log the error but don't fail the request
            Console.WriteLine($"Failed to save AI response: {ex.Message}");
        }

        // Simulate streaming by breaking the response into chunks
        const int chunkSize = 5; // Characters per chunk
        const int delayMs = 50; // Milliseconds between chunks

        await Response.StartAsync();

        for (int i = 0; i < reply.Length; i += chunkSize)
        {
            var end = Math.Min(i + chunkSize, reply.Length);
            var chunk = reply.Substring(i, end - i);
            var isDone = end >= reply.Length;

            var streamResponse = new StreamingChatResponse
            {
                Message = new ChatMessage
                {
                    Role = "assistant",
                    Content = chunk
                },
                Done = isDone
            };

            var jsonChunk = JsonSerializer.Serialize(streamResponse) + "\n";
            var bytes = Encoding.UTF8.GetBytes(jsonChunk);

            await Response.Body.WriteAsync(bytes);
            await Response.Body.FlushAsync();

            // Add delay between chunks (except for the last one)
            if (!isDone)
            {
                await Task.Delay(delayMs);
            }
        }

        return new EmptyResult();
    }

    private async Task<IActionResult> NonStreamResponse(string content, Guid userId, string sessionId, string model)
    {
        var reply = await _ollama.AskAsync(content);

        // Save AI response to database
        try
        {
            await _chatStorage.SaveMessageAsync(userId, new CreateChatMessageRequest
            {
                Message = reply,
                Role = "assistant",
                SessionId = sessionId,
                Model = model
            });
        }
        catch (Exception ex)
        {
            // Log the error but don't fail the request
            Console.WriteLine($"Failed to save AI response: {ex.Message}");
        }

        var response = new ChatResponse
        {
            Message = new ChatMessage
            {
                Role = "assistant",
                Content = reply
            },
            Done = true
        };

        return Ok(response);
    }


}
