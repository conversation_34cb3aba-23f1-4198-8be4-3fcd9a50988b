using Microsoft.AspNetCore.Mvc;
using AssistantService.Models;
using AssistantService.Services;

namespace AssistantService.Controllers;

/// <summary>
/// Controller for managing chat history and sessions
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class ChatHistoryController : ControllerBase
{
    private readonly IChatStorageService _chatStorage;
    private readonly ILogger<ChatHistoryController> _logger;

    public ChatHistoryController(IChatStorageService chatStorage, ILogger<ChatHistoryController> logger)
    {
        _chatStorage = chatStorage;
        _logger = logger;
    }

    /// <summary>
    /// Get chat history for the current user
    /// </summary>
    /// <param name="request">Chat history request with filters and pagination</param>
    /// <returns>Paginated chat history</returns>
    [HttpPost("history")]
    public async Task<ActionResult<ChatHistoryResponse>> GetChatHistory([FromBody] ChatHistoryRequest request)
    {
        try
        {
            // For now, use a default user ID since we don't have authentication
            // In production, this should come from the authenticated user
            var userId = Guid.Parse("00000000-0000-0000-0000-000000000001");

            var history = await _chatStorage.GetChatHistoryAsync(userId, request);
            return Ok(history);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving chat history");
            return StatusCode(500, "Failed to retrieve chat history");
        }
    }

    /// <summary>
    /// Get chat sessions for the current user
    /// </summary>
    /// <param name="page">Page number (default: 1)</param>
    /// <param name="pageSize">Page size (default: 20)</param>
    /// <returns>List of chat sessions</returns>
    [HttpGet("sessions")]
    public async Task<ActionResult<List<ChatSessionResponse>>> GetChatSessions(
        [FromQuery] int page = 1, 
        [FromQuery] int pageSize = 20)
    {
        try
        {
            // Validate pagination parameters
            if (page < 1) page = 1;
            if (pageSize < 1 || pageSize > 100) pageSize = 20;

            // For now, use a default user ID since we don't have authentication
            // In production, this should come from the authenticated user
            var userId = Guid.Parse("00000000-0000-0000-0000-000000000001");

            var sessions = await _chatStorage.GetChatSessionsAsync(userId, page, pageSize);
            return Ok(sessions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving chat sessions");
            return StatusCode(500, "Failed to retrieve chat sessions");
        }
    }

    /// <summary>
    /// Get messages for a specific chat session
    /// </summary>
    /// <param name="sessionId">Session ID</param>
    /// <param name="page">Page number (default: 1)</param>
    /// <param name="pageSize">Page size (default: 50)</param>
    /// <returns>Messages in the session</returns>
    [HttpGet("sessions/{sessionId}/messages")]
    public async Task<ActionResult<ChatHistoryResponse>> GetSessionMessages(
        string sessionId,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 50)
    {
        try
        {
            // Validate parameters
            if (string.IsNullOrWhiteSpace(sessionId))
            {
                return BadRequest("Session ID is required");
            }

            if (page < 1) page = 1;
            if (pageSize < 1 || pageSize > 100) pageSize = 50;

            // For now, use a default user ID since we don't have authentication
            // In production, this should come from the authenticated user
            var userId = Guid.Parse("00000000-0000-0000-0000-000000000001");

            var messages = await _chatStorage.GetSessionMessagesAsync(userId, sessionId, page, pageSize);
            return Ok(messages);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving session messages for session {SessionId}", sessionId);
            return StatusCode(500, "Failed to retrieve session messages");
        }
    }

    /// <summary>
    /// Delete a specific chat message
    /// </summary>
    /// <param name="messageId">Message ID to delete</param>
    /// <returns>Success status</returns>
    [HttpDelete("messages/{messageId}")]
    public async Task<IActionResult> DeleteMessage(Guid messageId)
    {
        try
        {
            // For now, use a default user ID since we don't have authentication
            // In production, this should come from the authenticated user
            var userId = Guid.Parse("00000000-0000-0000-0000-000000000001");

            var deleted = await _chatStorage.DeleteMessageAsync(userId, messageId);
            
            if (!deleted)
            {
                return NotFound("Message not found or you don't have permission to delete it");
            }

            return Ok(new { message = "Message deleted successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting message {MessageId}", messageId);
            return StatusCode(500, "Failed to delete message");
        }
    }

    /// <summary>
    /// Delete an entire chat session
    /// </summary>
    /// <param name="sessionId">Session ID to delete</param>
    /// <returns>Number of messages deleted</returns>
    [HttpDelete("sessions/{sessionId}")]
    public async Task<IActionResult> DeleteSession(string sessionId)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(sessionId))
            {
                return BadRequest("Session ID is required");
            }

            // For now, use a default user ID since we don't have authentication
            // In production, this should come from the authenticated user
            var userId = Guid.Parse("00000000-0000-0000-0000-000000000001");

            var deletedCount = await _chatStorage.DeleteSessionAsync(userId, sessionId);
            
            if (deletedCount == 0)
            {
                return NotFound("Session not found or you don't have permission to delete it");
            }

            return Ok(new { message = $"Session deleted successfully. {deletedCount} messages removed." });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting session {SessionId}", sessionId);
            return StatusCode(500, "Failed to delete session");
        }
    }

    /// <summary>
    /// Update the title of a conversation
    /// </summary>
    /// <param name="sessionId">Session ID</param>
    /// <param name="request">Request containing the new title</param>
    /// <returns>Success status</returns>
    [HttpPut("sessions/{sessionId}/title")]
    public async Task<IActionResult> UpdateConversationTitle(string sessionId, [FromBody] UpdateTitleRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(sessionId))
            {
                return BadRequest("Session ID is required");
            }

            if (request == null || string.IsNullOrWhiteSpace(request.Title))
            {
                return BadRequest("Title is required");
            }

            // For now, use a default user ID since we don't have authentication
            // In production, this should come from the authenticated user
            var userId = Guid.Parse("00000000-0000-0000-0000-000000000001");

            var updated = await _chatStorage.UpdateConversationTitleAsync(userId, sessionId, request.Title);
            
            if (!updated)
            {
                return NotFound("Session not found or you don't have permission to update it");
            }

            return Ok(new { message = "Conversation title updated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating title for session {SessionId}", sessionId);
            return StatusCode(500, "Failed to update conversation title");
        }
    }
}

/// <summary>
/// Request model for updating conversation title
/// </summary>
public class UpdateTitleRequest
{
    public string Title { get; set; } = string.Empty;
}
