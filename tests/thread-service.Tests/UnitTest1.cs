﻿using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.EntityFrameworkCore;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using ThreadService.Data;
using ThreadService.DTOs;
using ThreadService.Tests.TestHelpers;

namespace ThreadService.Tests;

public class ThreadServiceIntegrationTests : IClassFixture<TestWebApplicationFactory<Program>>
{
    private readonly TestWebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;

    public ThreadServiceIntegrationTests(TestWebApplicationFactory<Program> factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
    }

    [Fact]
    public async Task GetApiStatus_ReturnsOk()
    {
        // Act
        var response = await _client.GetAsync("/api/test");

        // Assert
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);

        var content = await response.Content.ReadAsStringAsync();
        Assert.Contains("ThreadService API is working!", content);
    }

    [Fact]
    public async Task GetThreads_ReturnsOk()
    {
        // Act
        var response = await _client.GetAsync("/api/threads");

        // Assert
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);

        var content = await response.Content.ReadAsStringAsync();
        var feedResponse = JsonSerializer.Deserialize<ThreadFeedResponse>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        Assert.NotNull(feedResponse);
        Assert.NotNull(feedResponse.Threads);
    }

    [Fact]
    public async Task GetComments_ReturnsOk()
    {
        // Act
        var response = await _client.GetAsync("/api/comments");

        // Assert
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);

        var content = await response.Content.ReadAsStringAsync();
        var feedResponse = JsonSerializer.Deserialize<CommentFeedResponse>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        Assert.NotNull(feedResponse);
        Assert.NotNull(feedResponse.Comments);
    }

    [Fact]
    public async Task HealthCheck_ReturnsHealthy()
    {
        // Act
        var response = await _client.GetAsync("/health");

        // Assert
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);

        var content = await response.Content.ReadAsStringAsync();
        Assert.Equal("Healthy", content);
    }

    [Fact]
    public async Task ReadinessCheck_ReturnsHealthy()
    {
        // Act
        var response = await _client.GetAsync("/health/ready");

        // Assert
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);

        var content = await response.Content.ReadAsStringAsync();
        Assert.Equal("Healthy", content);
    }

    [Fact]
    public async Task LivenessCheck_ReturnsHealthy()
    {
        // Act
        var response = await _client.GetAsync("/health/live");

        // Assert
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);

        var content = await response.Content.ReadAsStringAsync();
        Assert.Equal("Healthy", content);
    }

    [Fact]
    public void DatabaseContext_CanBeResolved()
    {
        // Arrange
        using var scope = _factory.Services.CreateScope();

        // Act & Assert
        var context = scope.ServiceProvider.GetRequiredService<ThreadContext>();
        Assert.NotNull(context);

        // Verify it's using in-memory database
        Assert.True(context.Database.IsInMemory());
    }
}
