using System.Net;
using AssistantService.Services;
using AssistantService.Configuration;
using AssistantService.Data;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using Moq;

namespace AssistantService.Tests.TestHelpers;

public class TestWebApplicationFactory : WebApplicationFactory<Program>
{
    public Mock<IOllamaClient> MockOllamaClient { get; private set; } = null!;

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.ConfigureServices(services =>
        {
            // Remove the existing IOllamaClient registration
            var ollamaClientDescriptor = services.SingleOrDefault(d => d.ServiceType == typeof(IOllamaClient));
            if (ollamaClientDescriptor != null)
                services.Remove(ollamaClientDescriptor);

            // Create a mock IOllamaClient directly instead of trying to mock HttpClient
            MockOllamaClient = new Mock<IOllamaClient>();

            // Set up default mock response for integration tests
            MockOllamaClient
                .Setup(x => x.AskAsync(It.IsAny<string>()))
                .ReturnsAsync("Mocked response from Ollama");

            services.AddSingleton(MockOllamaClient.Object);

            // Remove existing database context registration (if any)
            var dbContextDescriptor = services.SingleOrDefault(d => d.ServiceType == typeof(DbContextOptions<AssistantDbContext>));
            if (dbContextDescriptor != null)
                services.Remove(dbContextDescriptor);

            var contextDescriptor = services.SingleOrDefault(d => d.ServiceType == typeof(AssistantDbContext));
            if (contextDescriptor != null)
                services.Remove(contextDescriptor);

            // Add in-memory database for testing
            services.AddDbContext<AssistantDbContext>(options =>
            {
                options.UseInMemoryDatabase("TestDb");
            });
        });

        // Configure test settings to disable security features
        builder.ConfigureAppConfiguration((context, config) =>
        {
            config.AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Security:EnableRateLimiting"] = "false",
                ["Security:EnablePromptInjectionDetection"] = "false",
                ["Security:EnableContentFiltering"] = "false"
            });
        });

        builder.UseEnvironment("Testing");
    }

    public void SetupMockResponse(string response, HttpStatusCode statusCode = HttpStatusCode.OK)
    {
        if (MockOllamaClient == null)
            throw new InvalidOperationException("MockOllamaClient is not initialized");

        MockOllamaClient
            .Setup(x => x.AskAsync(It.IsAny<string>()))
            .ReturnsAsync(response);
    }

    public void SetupMockException(Exception exception)
    {
        if (MockOllamaClient == null)
            throw new InvalidOperationException("MockOllamaClient is not initialized");

        MockOllamaClient
            .Setup(x => x.AskAsync(It.IsAny<string>()))
            .ThrowsAsync(exception);
    }

    public void VerifyOllamaCall(Times times)
    {
        if (MockOllamaClient == null)
            throw new InvalidOperationException("MockOllamaClient is not initialized");

        MockOllamaClient
            .Verify(x => x.AskAsync(It.IsAny<string>()), times);
    }

    public void ResetMock()
    {
        MockOllamaClient?.Reset();
    }
}
