<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net10.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <RuntimeIdentifiers>linux-x64;linux-musl-x64;win-x64;osx-x64</RuntimeIdentifiers>
    <NoWarn>NU1605</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="10.0.0-preview.6.25358.103" />
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.12.1" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.12.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Supabase" Version="1.1.1" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="7.2.0" />
  </ItemGroup>

</Project>
